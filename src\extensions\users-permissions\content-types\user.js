const schema = require('./user/schema.json');

module.exports = {
  schema,
  lifecycles: {
    // Lifecycle hooks for user management
    async beforeCreate(event) {
      const { data } = event.params;
      
      // Set default values if not provided
      if (!data.balance) {
        data.balance = 0;
      }
      
      if (!data.level) {
        data.level = 1;
      }
      
      if (!data.colabStatus) {
        data.colabStatus = 'Not_Waiting';
      }
      
      if (!data.commission) {
        data.commission = {
          totalCommission: 0,
          sharedCommission: 0,
          managerCommission: 0,
          pendingCommission: 0,
          withdrawalCommission: 0,
        };
      }
      
      if (!data.verified) {
        data.verified = false;
      }
      
      if (!data.updatedReferCode) {
        data.updatedReferCode = false;
      }
      
      if (!data.isZaloOA) {
        data.isZaloOA = false;
      }
    },
    
    async afterCreate(event) {
      const { result } = event;
      
      // Generate refer code after user creation
      if (!result.referCode) {
        const PREFIX_CODE = 'REF';
        const newReferCode = PREFIX_CODE + result.id;
        
        await strapi.db.query('plugin::users-permissions.user').update({
          where: { id: result.id },
          data: { referCode: newReferCode },
        });
      }
    },
    
    async beforeUpdate(event) {
      const { data } = event.params;
      
      // Handle rank updates
      if (data.rank) {
        // Log rank change for audit
        console.log(`User rank updated:`, {
          userId: event.params.where.id,
          newRank: data.rank,
          timestamp: new Date(),
        });
      }
      
      // Handle level updates
      if (data.level) {
        // Validate level is positive integer
        if (data.level < 1) {
          data.level = 1;
        }
      }
    },
  },
};
