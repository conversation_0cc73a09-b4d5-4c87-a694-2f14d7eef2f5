const { parseMultipartData, sanitizeEntity } = require("@strapi/utils");

module.exports = (plugin) => {
  plugin.controllers["content-api"].uploadAvatar = (ctx) => {
    ctx.request.body.refId = ctx.state.user.id;
    return plugin.controllers["content-api"].upload(ctx);
  };
  plugin.controllers["content-api"].uploadIdentity = (ctx) => {
    if (ctx.is("multipart")) {
      const { data, files } = parseMultipartData(ctx);
      console.log("🚀 ~ file: strapi-server.js:9 ~ data, files:", data, files);
    }
    // ctx.request.body.refId
    return {};
  };

  plugin.routes["content-api"].routes.push({
    method: "POST",
    path: "/avatar",
    handler: "content-api.uploadAvatar",
  });
  plugin.routes["content-api"].routes.push({
    method: "POST",
    path: "/identity",
    handler: "content-api.uploadIdentity",
  });
  return plugin;
};
