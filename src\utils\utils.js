const crypto = require("crypto");
const { Level, RankRequirements } = require("./constants");

/**
 * Validate registration body
 */
const validateRegisterBody = async (params) => {
  const { name, phone, email, password } = params;

  // Validate required fields
  if (!name || name.trim().length < 2) {
    throw new Error("Tên phải có ít nhất 2 ký tự");
  }

  if (!phone || !isValidPhone(phone)) {
    throw new Error("Số điện thoại không hợp lệ");
  }

  if (!email || !isValidEmail(email)) {
    throw new Error("Email không hợp lệ");
  }

  if (!password || password.length < 6) {
    throw new Error("Mật khẩu phải có ít nhất 6 ký tự");
  }

  return true;
};

/**
 * Validate callback body for login
 */
const validateCallbackBody = async (params) => {
  const { identifier, password } = params;

  // Validate required fields
  if (!identifier) {
    throw new Error("Số điện thoại hoặc email là bắt buộc");
  }

  if (!password) {
    throw new Error("Mật khẩu là bắt buộc");
  }

  return true;
};

/**
 * Convert 11-digit phone to 10-digit
 */
const convertPhone11To10 = (phone) => {
  if (!phone) return phone;

  // Remove all non-digit characters
  const cleanPhone = phone.replace(/\D/g, "");

  // If phone starts with 84, remove it
  if (cleanPhone.startsWith("84") && cleanPhone.length === 12) {
    return cleanPhone.substring(2);
  }

  // If phone starts with +84, remove it
  if (cleanPhone.startsWith("84") && cleanPhone.length === 11) {
    return cleanPhone.substring(2);
  }

  // If phone starts with 0 and has 11 digits, remove the 0
  if (cleanPhone.startsWith("0") && cleanPhone.length === 11) {
    return cleanPhone.substring(1);
  }

  return cleanPhone;
};

/**
 * Validate phone number
 */
const isValidPhone = (phone) => {
  const cleanPhone = convertPhone11To10(phone);
  // Vietnamese phone number pattern (10 digits, starts with specific prefixes)
  const phoneRegex = /^(3[2-9]|5[6|8|9]|7[0|6-9]|8[1-9]|9[0-9])[0-9]{7}$/;
  return phoneRegex.test(cleanPhone);
};

/**
 * Validate email
 */
const isValidEmail = (email) => {
  const emailRegex =
    /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/;
  return emailRegex.test(email);
};

/**
 * Simple password encryption/decryption for auto-generated passwords
 */
const encryptPassword = (text) => {
  const algorithm = "aes-256-cbc";
  const key = crypto.scryptSync("secret-key", "salt", 32);
  const iv = crypto.randomBytes(16);

  const cipher = crypto.createCipher(algorithm, key);
  let encrypted = cipher.update(text, "utf8", "hex");
  encrypted += cipher.final("hex");

  return encrypted;
};

const decryptPassword = (encryptedText) => {
  try {
    const algorithm = "aes-256-cbc";
    const key = crypto.scryptSync("secret-key", "salt", 32);

    const decipher = crypto.createDecipher(algorithm, key);
    let decrypted = decipher.update(encryptedText, "hex", "utf8");
    decrypted += decipher.final("utf8");

    return decrypted;
  } catch (error) {
    return null;
  }
};

/**
 * Calculate user level based on various criteria
 */
const calcUserLevel = async (userId) => {
  try {
    // Get user data
    const user = await strapi.db
      .query("plugin::users-permissions.user")
      .findOne({
        where: { id: userId },
        populate: { commission: true },
      });

    if (!user) return Level.NewUser;

    // Get user's orders
    const orders = await strapi.db.query("api::don-hang.don-hang").findMany({
      where: {
        userId: userId,
        status: "Đã hoàn thành",
      },
    });

    // Calculate total purchase amount
    const totalPurchase = orders.reduce(
      (sum, order) => sum + (order.totalAmount || 0),
      0
    );

    // Get total commission
    const totalCommission = user.commission?.totalCommission || 0;

    // Count referrals
    const referralCount = await strapi.db
      .query("plugin::users-permissions.user")
      .count({
        where: { fParent: userId },
      });

    // Determine level based on criteria
    if (
      totalPurchase >= RankRequirements.DIAMOND.minPurchase &&
      totalCommission >= RankRequirements.DIAMOND.minCommission &&
      referralCount >= RankRequirements.DIAMOND.minReferrals
    ) {
      return Level.President;
    }

    if (
      totalPurchase >= RankRequirements.PLATINUM.minPurchase &&
      totalCommission >= RankRequirements.PLATINUM.minCommission &&
      referralCount >= RankRequirements.PLATINUM.minReferrals
    ) {
      return Level.Director;
    }

    if (
      totalPurchase >= RankRequirements.GOLD.minPurchase &&
      totalCommission >= RankRequirements.GOLD.minCommission &&
      referralCount >= RankRequirements.GOLD.minReferrals
    ) {
      return Level.Manager;
    }

    if (
      totalPurchase >= RankRequirements.SILVER.minPurchase &&
      totalCommission >= RankRequirements.SILVER.minCommission &&
      referralCount >= RankRequirements.SILVER.minReferrals
    ) {
      return Level.Collaborator;
    }

    return Level.Member;
  } catch (error) {
    console.error("Error calculating user level:", error);
    return Level.NewUser;
  }
};

/**
 * Send notification based on level
 */
const sendNotiBaseOnLevel = async (user, newLevel) => {
  try {
    // Implementation for sending notifications
    // This could integrate with email, SMS, or push notification services
    console.log(`User ${user.id} upgraded to level ${newLevel}`);

    // You can implement actual notification logic here
    // For example: send email, create in-app notification, etc.

    return true;
  } catch (error) {
    console.error("Error sending level notification:", error);
    return false;
  }
};

/**
 * Track events for analytics
 */
const trackEvent = (eventName, data) => {
  try {
    // Implementation for event tracking
    // This could integrate with analytics services like Google Analytics, Mixpanel, etc.
    console.log(`Event: ${eventName}`, data);

    // You can implement actual tracking logic here

    return true;
  } catch (error) {
    console.error("Error tracking event:", error);
    return false;
  }
};

/**
 * Generate unique refer code
 */
const generateReferCode = (userId, prefix = "REF") => {
  return `${prefix}${userId}`;
};

/**
 * Validate refer code format
 */
const isValidReferCode = (referCode) => {
  if (!referCode) return false;
  // Check if refer code matches expected format
  const referCodeRegex = /^REF\d+$/;
  return referCodeRegex.test(referCode);
};

module.exports = {
  validateRegisterBody,
  validateCallbackBody,
  convertPhone11To10,
  isValidPhone,
  isValidEmail,
  encryptPassword,
  decryptPassword,
  calcUserLevel,
  sendNotiBaseOnLevel,
  trackEvent,
  generateReferCode,
  isValidReferCode,
};
