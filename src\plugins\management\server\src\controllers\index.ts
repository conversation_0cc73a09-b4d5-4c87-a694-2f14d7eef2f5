import controller from './controller';
import orderController from './order-controller';
import userController from './user-controller';
import withdrawalController from './withdrawal-controller';
import dashboardController from './dashboard-controller';
import productController from './product-controller';
import newsController from './news-controller';
import promotionController from './promotion-controller';
import commissionController from './commission-controller';
import settingsController from './settings-controller';

export default {
  controller,
  'order-controller': orderController,
  'user-controller': userController,
  'withdrawal-controller': withdrawalController,
  'dashboard-controller': dashboardController,
  'product-controller': productController,
  'news-controller': newsController,
  'promotion-controller': promotionController,
  'commission-controller': commissionController,
  'settings-controller': settingsController,
};
