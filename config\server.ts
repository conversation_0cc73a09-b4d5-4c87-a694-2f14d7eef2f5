export default ({ env }) => ({
  host: env("HOST", "0.0.0.0"),
  port: env.int("PORT", 1337),
  url: env("URL", "http://localhost:1337"),
  app: {
    keys: env.array("APP_KEYS"),
  },
  // Add timeout configurations for better upload stability
  http: {
    timeout: env.int("HTTP_TIMEOUT", 60000), // 60 seconds
    keepAliveTimeout: env.int("KEEP_ALIVE_TIMEOUT", 5000),
    headersTimeout: env.int("HEADERS_TIMEOUT", 60000),
  },
});
