"use strict";

const axios = require("axios");
const crypto = require("crypto");
const { ApplicationError } = require("@strapi/utils").errors;

/**
 * Zalo Mini App Service
 * Handles authentication and API interactions with Zalo
 */
class ZaloService {
  constructor() {
    this.baseURL = "https://graph.zalo.me";
    this.oaURL = "https://openapi.zalo.me";
  }

  /**
   * Get Zalo Mini App settings from database
   */
  async getZaloSettings() {
    try {
      const settings = await strapi.db
        .query("api::cai-dat-zalo-mini-app.cai-dat-zalo-mini-app")
        .findOne();

      if (!settings) {
        throw new ApplicationError("Zalo Mini App chưa được cấu hình");
      }

      return settings;
    } catch (error) {
      console.error("Error getting Zalo settings:", error);
      throw new ApplicationError("Không thể tải cài đặt Zalo Mini App");
    }
  }

  /**
   * Validate Zalo access token and get user info
   * @param {string} accessToken - Zalo access token from Mini App
   * @returns {Object} User information from Zalo
   */
  async validateAccessToken(accessToken) {
    try {
      if (!accessToken) {
        throw new ApplicationError("Access token không được cung cấp");
      }

      // Call Zalo API to get user info
      const response = await axios.get(`${this.baseURL}/v2.0/me`, {
        params: {
          access_token: accessToken,
          fields: "id,name,picture",
        },
        timeout: 10000,
      });

      if (response.data.error) {
        console.error("Zalo API error:", response.data.error);
        throw new ApplicationError("Access token không hợp lệ");
      }

      const userData = response.data;

      // Validate required fields
      if (!userData.id) {
        throw new ApplicationError(
          "Không thể lấy thông tin người dùng từ Zalo"
        );
      }

      return {
        zaloId: userData.id,
        zaloName: userData.name || "",
        zaloAvatar: userData.picture?.data?.url || "",
        isValid: true,
      };
    } catch (error) {
      console.error("Error validating Zalo access token:", error);

      if (error.response) {
        // Zalo API returned an error
        const status = error.response.status;
        const message =
          error.response.data?.error?.message || "Lỗi xác thực Zalo";

        if (status === 401 || status === 400) {
          throw new ApplicationError(
            "Access token không hợp lệ hoặc đã hết hạn"
          );
        }

        throw new ApplicationError(`Lỗi Zalo API: ${message}`);
      }

      if (error.code === "ECONNABORTED") {
        throw new ApplicationError("Timeout khi kết nối với Zalo API");
      }

      throw new ApplicationError("Không thể xác thực với Zalo");
    }
  }

  /**
   * Verify Zalo Mini App signature for security
   * @param {Object} data - Request data from Mini App
   * @param {string} signature - Signature from Mini App
   * @returns {boolean} Is signature valid
   */
  async verifySignature(data, signature) {
    try {
      const settings = await this.getZaloSettings();
      const appSecret = settings.appSecret;

      if (!appSecret) {
        console.warn(
          "Zalo App Secret not configured, skipping signature verification"
        );
        return true; // Allow if not configured for development
      }

      // Create signature from data
      const sortedKeys = Object.keys(data).sort();
      const signatureString = sortedKeys
        .map((key) => `${key}=${data[key]}`)
        .join("&");

      const expectedSignature = crypto
        .createHmac("sha256", appSecret)
        .update(signatureString)
        .digest("hex");

      return signature === expectedSignature;
    } catch (error) {
      console.error("Error verifying Zalo signature:", error);
      return false;
    }
  }

  /**
   * Get user phone number from Zalo (requires special permission)
   * @param {string} accessToken - Zalo access token
   * @param {string} code - Phone verification code
   * @returns {string} Phone number
   */
  async getUserPhone(accessToken, code) {
    try {
      const response = await axios.get(`${this.baseURL}/v2.0/me/phone`, {
        params: {
          access_token: accessToken,
          code: code,
        },
        timeout: 10000,
      });

      if (response.data.error) {
        throw new ApplicationError("Không thể lấy số điện thoại từ Zalo");
      }

      return response.data.number;
    } catch (error) {
      console.error("Error getting user phone from Zalo:", error);
      throw new ApplicationError("Không thể lấy số điện thoại từ Zalo");
    }
  }

  /**
   * Send OA message to user (if OA is configured)
   * @param {string} userId - Zalo user ID
   * @param {string} message - Message to send
   */
  async sendOAMessage(userId, message) {
    try {
      const settings = await this.getZaloSettings();

      if (!settings.oaId) {
        console.log("Zalo OA not configured, skipping message send");
        return;
      }

      // Implementation depends on your OA setup
      console.log(`Would send OA message to ${userId}: ${message}`);
    } catch (error) {
      console.error("Error sending OA message:", error);
      // Don't throw error for OA messages as it's not critical
    }
  }

  /**
   * Log Zalo authentication event for audit
   * @param {string} zaloId - Zalo user ID
   * @param {string} action - Action performed
   * @param {Object} metadata - Additional data
   */
  async logAuthEvent(zaloId, action, metadata = {}) {
    try {
      console.log("Zalo Auth Event:", {
        zaloId,
        action,
        metadata,
        timestamp: new Date().toISOString(),
      });

      // You can extend this to save to database for audit trail
    } catch (error) {
      console.error("Error logging Zalo auth event:", error);
    }
  }
}

module.exports = new ZaloService();
