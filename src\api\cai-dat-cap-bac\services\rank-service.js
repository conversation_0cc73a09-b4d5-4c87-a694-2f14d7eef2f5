'use strict';

const { DefaultRanks, RankRequirements } = require('../../../utils/constants');

/**
 * Rank service for managing user ranks and rank system
 */
module.exports = {
  /**
   * Get rank settings
   */
  async getRankSettings() {
    try {
      let settings = await strapi.db.query('api::cai-dat-cap-bac.cai-dat-cap-bac').findOne();
      
      if (!settings) {
        // Create default settings if none exist
        settings = await this.createDefaultRankSettings();
      }
      
      return settings;
    } catch (error) {
      console.error('Error getting rank settings:', error);
      throw error;
    }
  },

  /**
   * Create default rank settings
   */
  async createDefaultRankSettings() {
    try {
      const defaultSettings = {
        ranks: DefaultRanks,
        enableRankSystem: true,
        autoUpgradeEnabled: true,
        upgradeRequirements: {
          byPurchase: true,
          byCommission: true,
          byReferrals: true,
        },
      };

      const settings = await strapi.db.query('api::cai-dat-cap-bac.cai-dat-cap-bac').create({
        data: defaultSettings,
      });

      return settings;
    } catch (error) {
      console.error('Error creating default rank settings:', error);
      throw error;
    }
  },

  /**
   * Update rank settings
   */
  async updateRankSettings(data) {
    try {
      let settings = await strapi.db.query('api::cai-dat-cap-bac.cai-dat-cap-bac').findOne();
      
      if (settings) {
        settings = await strapi.db.query('api::cai-dat-cap-bac.cai-dat-cap-bac').update({
          where: { id: settings.id },
          data,
        });
      } else {
        settings = await strapi.db.query('api::cai-dat-cap-bac.cai-dat-cap-bac').create({
          data,
        });
      }
      
      return settings;
    } catch (error) {
      console.error('Error updating rank settings:', error);
      throw error;
    }
  },

  /**
   * Get user's current rank
   */
  async getUserRank(userId) {
    try {
      const user = await strapi.db.query('plugin::users-permissions.user').findOne({
        where: { id: userId },
      });
      
      if (!user || !user.rank) {
        // Return default rank if user has no rank
        return this.getDefaultRank();
      }
      
      return user.rank;
    } catch (error) {
      console.error('Error getting user rank:', error);
      throw error;
    }
  },

  /**
   * Get default rank (lowest rank)
   */
  getDefaultRank() {
    return DefaultRanks[0]; // Bronze rank
  },

  /**
   * Calculate user's eligible rank based on their activity
   */
  async calculateEligibleRank(userId) {
    try {
      // Get user data
      const user = await strapi.db.query('plugin::users-permissions.user').findOne({
        where: { id: userId },
        populate: { commission: true },
      });
      
      if (!user) {
        throw new Error('User not found');
      }
      
      // Get user's completed orders
      const orders = await strapi.db.query('api::don-hang.don-hang').findMany({
        where: { 
          userId: userId,
          status: 'Đã hoàn thành',
        },
      });
      
      // Calculate total purchase amount
      const totalPurchase = orders.reduce((sum, order) => sum + (order.totalAmount || 0), 0);
      
      // Get total commission
      const totalCommission = user.commission?.totalCommission || 0;
      
      // Count direct referrals
      const referralCount = await strapi.db.query('plugin::users-permissions.user').count({
        where: { fParent: userId },
      });
      
      // Get rank settings
      const settings = await this.getRankSettings();
      const ranks = settings.ranks || DefaultRanks;
      
      // Find the highest rank the user qualifies for
      let eligibleRank = ranks[0]; // Default to lowest rank
      
      for (const rank of ranks.reverse()) { // Start from highest rank
        const requirements = rank.requirements || RankRequirements.BRONZE;
        
        if (totalPurchase >= requirements.minPurchase &&
            totalCommission >= requirements.minCommission &&
            referralCount >= requirements.minReferrals) {
          eligibleRank = rank;
          break;
        }
      }
      
      return eligibleRank;
    } catch (error) {
      console.error('Error calculating eligible rank:', error);
      throw error;
    }
  },

  /**
   * Update user rank
   */
  async updateUserRank(userId, rankData) {
    try {
      const updatedUser = await strapi.db.query('plugin::users-permissions.user').update({
        where: { id: userId },
        data: { 
          rank: rankData,
          level: rankData.level || 1,
        },
      });
      
      // Log rank change
      console.log(`User ${userId} rank updated to:`, rankData.name);
      
      return updatedUser;
    } catch (error) {
      console.error('Error updating user rank:', error);
      throw error;
    }
  },

  /**
   * Auto-upgrade user rank if eligible
   */
  async autoUpgradeUserRank(userId) {
    try {
      const settings = await this.getRankSettings();
      
      if (!settings.autoUpgradeEnabled) {
        return null; // Auto-upgrade is disabled
      }
      
      const currentRank = await this.getUserRank(userId);
      const eligibleRank = await this.calculateEligibleRank(userId);
      
      // Check if user is eligible for a higher rank
      if (eligibleRank.level > currentRank.level) {
        await this.updateUserRank(userId, eligibleRank);
        
        // Send notification about rank upgrade
        const user = await strapi.db.query('plugin::users-permissions.user').findOne({
          where: { id: userId },
        });
        
        // You can implement notification logic here
        console.log(`User ${user.name} (${user.id}) upgraded from ${currentRank.name} to ${eligibleRank.name}`);
        
        return eligibleRank;
      }
      
      return null; // No upgrade needed
    } catch (error) {
      console.error('Error auto-upgrading user rank:', error);
      throw error;
    }
  },

  /**
   * Get all available ranks
   */
  async getAllRanks() {
    try {
      const settings = await this.getRankSettings();
      return settings.ranks || DefaultRanks;
    } catch (error) {
      console.error('Error getting all ranks:', error);
      return DefaultRanks;
    }
  },

  /**
   * Get rank by level
   */
  async getRankByLevel(level) {
    try {
      const ranks = await this.getAllRanks();
      return ranks.find(rank => rank.level === level) || this.getDefaultRank();
    } catch (error) {
      console.error('Error getting rank by level:', error);
      return this.getDefaultRank();
    }
  },
};
