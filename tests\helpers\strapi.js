const Strapi = require('@strapi/strapi');
const fs = require('fs');

let instance;

/**
 * Setup Strapi instance for testing
 */
async function setupStrapi() {
  if (!instance) {
    await Strapi().load();
    instance = strapi;

    // Setup test database
    await instance.db.connection.raw('PRAGMA foreign_keys = OFF;');
    
    // Clean up existing data
    await cleanupDatabase();
    
    // Create default roles if they don't exist
    await setupDefaultRoles();
  }

  return instance;
}

/**
 * Cleanup Strapi instance
 */
async function cleanupStrapi() {
  if (instance) {
    await cleanupDatabase();
    await instance.destroy();
    instance = null;
  }
}

/**
 * Clean up test database
 */
async function cleanupDatabase() {
  if (!instance) return;

  try {
    // Clean up users
    await instance.db.query('plugin::users-permissions.user').deleteMany({});
    
    // Clean up other test data
    await instance.db.query('api::don-hang.don-hang').deleteMany({});
    await instance.db.query('api::hoa-hong.hoa-hong').deleteMany({});
    
    console.log('Test database cleaned up');
  } catch (error) {
    console.warn('Error cleaning up database:', error);
  }
}

/**
 * Setup default roles for testing
 */
async function setupDefaultRoles() {
  try {
    // Check if roles exist
    const existingRoles = await instance.db.query('plugin::users-permissions.role').findMany({});
    
    if (existingRoles.length === 0) {
      // Create default roles
      await instance.db.query('plugin::users-permissions.role').create({
        data: {
          name: 'Authenticated',
          description: 'Default role given to authenticated user.',
          type: 'authenticated'
        }
      });

      await instance.db.query('plugin::users-permissions.role').create({
        data: {
          name: 'Public',
          description: 'Default role given to unauthenticated user.',
          type: 'public'
        }
      });

      console.log('Default roles created for testing');
    }
  } catch (error) {
    console.warn('Error setting up default roles:', error);
  }
}

/**
 * Create test user
 */
async function createTestUser(userData = {}) {
  const defaultUserData = {
    username: 'testuser',
    email: '<EMAIL>',
    password: 'password123',
    name: 'Test User',
    phone: '0987654321',
    confirmed: true,
    blocked: false,
    balance: 0,
    ...userData
  };

  // Get default role
  const role = await instance.db.query('plugin::users-permissions.role').findOne({
    where: { type: 'authenticated' }
  });

  if (role) {
    defaultUserData.role = role.id;
  }

  return await instance.plugins['users-permissions'].services.user.add(defaultUserData);
}

/**
 * Generate JWT token for user
 */
function generateJWT(user) {
  return instance.plugins['users-permissions'].services.jwt.issue({ id: user.id });
}

/**
 * Mock Zalo service for testing
 */
function mockZaloService(mockImplementation = {}) {
  const defaultMock = {
    validateAccessToken: jest.fn().mockResolvedValue({
      isValid: true,
      zaloId: 'test_zalo_id',
      zaloName: 'Test Zalo User',
      zaloAvatar: 'https://example.com/avatar.jpg'
    }),
    logAuthEvent: jest.fn(),
    sendOAMessage: jest.fn(),
    verifySignature: jest.fn().mockResolvedValue(true)
  };

  const zaloService = require('../../src/services/zalo-service');
  Object.assign(zaloService, { ...defaultMock, ...mockImplementation });
  
  return zaloService;
}

/**
 * Create test Zalo settings
 */
async function createTestZaloSettings(settings = {}) {
  const defaultSettings = {
    oaId: 'test_oa_id',
    appId: 'test_app_id',
    appSecret: 'test_app_secret',
    checkoutPrivateKey: 'test_private_key',
    ...settings
  };

  return await instance.db.query('api::cai-dat-zalo-mini-app.cai-dat-zalo-mini-app').create({
    data: defaultSettings
  });
}

module.exports = {
  setupStrapi,
  cleanupStrapi,
  cleanupDatabase,
  createTestUser,
  generateJWT,
  mockZaloService,
  createTestZaloSettings
};
