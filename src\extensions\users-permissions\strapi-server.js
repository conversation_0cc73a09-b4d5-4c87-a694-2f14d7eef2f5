"use strict";
const _ = require("lodash");
const utils = require("@strapi/utils");
const { getService } = require("@strapi/plugin-users-permissions/server/utils");
const { sanitize } = require("@strapi/utils");
const { ApplicationError } = utils.errors;
const user = require("./content-types/user");
const zaloService = require("../../services/zalo-service");

const sanitizeUser = (user, ctx) => {
  const { auth } = ctx.state;
  const userSchema = strapi.getModel("plugin::users-permissions.user");
  return sanitize.contentAPI.output(user, userSchema, { auth });
};

const getController = (name) => {
  return strapi.plugins["users-permissions"].controller(name);
};

module.exports = (plugin) => {
  plugin.contentTypes.user = user;

  // Zalo Mini App Authentication
  plugin.controllers.auth.register = async (ctx) => {
    try {
      const { accessToken, phone, referCode } = ctx.request.body;

      if (!accessToken) {
        throw new ApplicationError("Access token Zalo là bắt buộc");
      }

      // Validate Zalo access token and get user info
      const zaloUserInfo = await zaloService.validateAccessToken(accessToken);

      if (!zaloUserInfo.isValid) {
        throw new ApplicationError("Access token Zalo không hợp lệ");
      }

      // Check if user already exists with this Zalo ID
      let existingUser = await strapi
        .query("plugin::users-permissions.user")
        .findOne({
          where: { zaloId: zaloUserInfo.zaloId },
        });

      if (existingUser) {
        // Update last login and token
        existingUser = await strapi
          .query("plugin::users-permissions.user")
          .update({
            where: { id: existingUser.id },
            data: {
              zaloAccessToken: accessToken,
              lastZaloLogin: new Date(),
              zaloName: zaloUserInfo.zaloName,
              zaloAvatar: zaloUserInfo.zaloAvatar,
            },
          });

        // Log auth event
        await zaloService.logAuthEvent(zaloUserInfo.zaloId, "login", {
          userId: existingUser.id,
          phone: existingUser.phone,
        });

        // Generate JWT
        const jwt = getService("jwt").issue(_.pick(existingUser, ["id"]));
        const sanitizedUser = await sanitizeUser(existingUser, ctx);

        return ctx.send({
          jwt,
          user: sanitizedUser,
          isNewUser: false,
        });
      }

      // Check if registration is allowed
      const pluginStore = await strapi.store({
        type: "plugin",
        name: "users-permissions",
      });
      const settings = await pluginStore.get({ key: "advanced" });

      if (!settings.allow_register) {
        throw new ApplicationError(
          "Đăng ký mới hiện tại không được phép. Vui lòng liên hệ quản trị viên."
        );
      }

      // Validate phone number if provided
      if (phone) {
        const phoneRegex = /^(3[2-9]|5[6|8|9]|7[0|6-9]|8[1-9]|9[0-9])[0-9]{7}$/;
        if (!phoneRegex.test(phone)) {
          throw new ApplicationError("Số điện thoại không hợp lệ");
        }

        // Check if phone already exists
        const existingPhoneUser = await strapi
          .query("plugin::users-permissions.user")
          .findOne({
            where: { phone },
          });

        if (existingPhoneUser) {
          throw new ApplicationError("Số điện thoại đã được sử dụng");
        }
      }

      // Handle referral code
      let referUser = null;
      if (referCode) {
        referUser = await strapi
          .query("plugin::users-permissions.user")
          .findOne({
            where: {
              phone: referCode,
              confirmed: true,
            },
          });

        if (!referUser) {
          throw new ApplicationError(
            "Số điện thoại người giới thiệu không hợp lệ"
          );
        }
      }

      // Get default role
      const role = await strapi
        .query("plugin::users-permissions.role")
        .findOne({ where: { type: settings.default_role } });

      // Create new user
      const newUserData = {
        username: `zalo_${zaloUserInfo.zaloId}`,
        email: `zalo_${zaloUserInfo.zaloId}@zalo.temp`,
        name: zaloUserInfo.zaloName || `Zalo User ${zaloUserInfo.zaloId}`,
        phone: phone || null,
        zaloId: zaloUserInfo.zaloId,
        zaloName: zaloUserInfo.zaloName,
        zaloAvatar: zaloUserInfo.zaloAvatar,
        zaloAccessToken: accessToken,
        lastZaloLogin: new Date(),
        role: role.id,
        confirmed: true,
        balance: 0,
        rank: 1,
        referUser: referUser?.id || null,
        provider: "zalo",
      };

      const newUser = await getService("user").add(newUserData);

      // Log auth event
      await zaloService.logAuthEvent(zaloUserInfo.zaloId, "register", {
        userId: newUser.id,
        phone: phone,
        hasReferrer: !!referUser,
      });

      // Send welcome message via OA if configured
      await zaloService.sendOAMessage(
        zaloUserInfo.zaloId,
        `Chào mừng ${zaloUserInfo.zaloName} đến với hệ thống!`
      );

      const sanitizedUser = await sanitizeUser(newUser, ctx);
      const jwt = getService("jwt").issue(_.pick(newUser, ["id"]));

      return ctx.send({
        jwt,
        user: sanitizedUser,
        isNewUser: true,
      });
    } catch (error) {
      console.error("Zalo auth error:", error);

      if (error instanceof ApplicationError) {
        throw error;
      }

      throw new ApplicationError("Lỗi xác thực Zalo. Vui lòng thử lại.");
    }
  };

  plugin.controllers.user.updateMe = async (ctx) => {
    const user = ctx.state.user;
    // User has to be logged in to update themselves
    if (!user) {
      return ctx.unauthorized();
    }
    // Pick only specific fields that exist in user schema
    const newData = _.pick(ctx.request.body, [
      "email",
      "name",
      "phone",
      "taxCode",
      "avatarUrl",
      "isZaloOA",
      "bankName",
      "bankAccount",
      "bankOwner",
      "cccd",
      "zaloName",
      "zaloAvatar",
    ]);

    if (newData.email) {
      const validRegexEmail =
        /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/;

      const userWithSameEmail = await strapi
        .query("plugin::users-permissions.user")
        .findOne({
          where: { email: newData.email.toLowerCase() },
        });

      if (userWithSameEmail && userWithSameEmail.id != user.id) {
        return ctx.badRequest("Email đã tồn tại");
      }

      if (!newData.email.match(validRegexEmail)) {
        return ctx.badRequest("Email không đúng định dạng");
      }
      newData.email = newData.email.toLowerCase();
    }

    if (newData.taxCode) {
      const validRegexTaxCode = /^\d+$/;
      if (!newData.taxCode.match(validRegexTaxCode)) {
        return ctx.badRequest("Mã số thuế không đúng định dạng");
      }
    }

    ctx.request.body = newData;
    ctx.params = { id: user.id };

    const res = await getController("user").update(ctx);

    return res;
  };

  // Add Zalo authentication method for linking existing account
  plugin.controllers.auth.linkZaloAccount = async (ctx) => {
    try {
      const user = ctx.state.user;
      if (!user) {
        return ctx.unauthorized("Bạn cần đăng nhập để liên kết tài khoản Zalo");
      }

      const { accessToken } = ctx.request.body;
      if (!accessToken) {
        throw new ApplicationError("Access token Zalo là bắt buộc");
      }

      // Validate Zalo access token
      const zaloUserInfo = await zaloService.validateAccessToken(accessToken);

      if (!zaloUserInfo.isValid) {
        throw new ApplicationError("Access token Zalo không hợp lệ");
      }

      // Check if this Zalo ID is already linked to another account
      const existingZaloUser = await strapi
        .query("plugin::users-permissions.user")
        .findOne({
          where: {
            zaloId: zaloUserInfo.zaloId,
            id: { $ne: user.id },
          },
        });

      if (existingZaloUser) {
        throw new ApplicationError(
          "Tài khoản Zalo này đã được liên kết với tài khoản khác"
        );
      }

      // Update current user with Zalo info
      const updatedUser = await strapi
        .query("plugin::users-permissions.user")
        .update({
          where: { id: user.id },
          data: {
            zaloId: zaloUserInfo.zaloId,
            zaloName: zaloUserInfo.zaloName,
            zaloAvatar: zaloUserInfo.zaloAvatar,
            zaloAccessToken: accessToken,
            lastZaloLogin: new Date(),
          },
        });

      // Log auth event
      await zaloService.logAuthEvent(zaloUserInfo.zaloId, "link_account", {
        userId: user.id,
        phone: user.phone,
      });

      const sanitizedUser = await sanitizeUser(updatedUser, ctx);

      return ctx.send({
        user: sanitizedUser,
        message: "Liên kết tài khoản Zalo thành công",
      });
    } catch (error) {
      console.error("Link Zalo account error:", error);

      if (error instanceof ApplicationError) {
        throw error;
      }

      throw new ApplicationError(
        "Lỗi liên kết tài khoản Zalo. Vui lòng thử lại."
      );
    }
  };

  plugin.routes["content-api"].routes.unshift({
    method: "POST",
    path: "/auth/link-zalo",
    handler: "auth.linkZaloAccount",
    config: {
      prefix: "",
      middlewares: ["plugin::users-permissions.rateLimit"],
    },
  });

  plugin.routes["content-api"].routes.unshift({
    method: "PUT",
    path: "/users/me",
    handler: "user.updateMe",
    config: {
      prefix: "",
    },
  });

  plugin.routes["content-api"].routes.unshift({
    method: "PUT",
    path: "/users/register-collaborator",
    handler: "user.registerCollabration",
    config: {
      prefix: "",
    },
  });

  return plugin;
};
