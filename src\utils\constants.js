// User Levels
const Level = {
  NewUser: 0,
  Member: 1,
  Collaborator: 2,
  Manager: 3,
  Director: 4,
  President: 5,
};

// Collaboration Status
const ColabStatus = {
  Not_Waiting: "Not_Waiting",
  Waiting: "Waiting",
  Approved: "Approved",
  Rejected: "Rejected",
};

// Transaction Types
const TransactionType = {
  PendingCommissionRefer: "PendingCommissionRefer",
  CommissionApproved: "CommissionApproved",
  CommissionWithdraw: "CommissionWithdraw",
  BalanceDeposit: "BalanceDeposit",
  BalanceWithdraw: "BalanceWithdraw",
  Purchase: "Purchase",
  Refund: "Refund",
};

// Commission Settings
const DirectCommission = {
  [Level.NewUser]: 0,
  [Level.Member]: 50000,
  [Level.Collaborator]: 100000,
  [Level.Manager]: 200000,
  [Level.Director]: 500000,
  [Level.President]: 1000000,
};

const NewUserRegisterCommissionValue = 50000;
const NewUserRegisterCommissionValueForPresident = 100000;

// Rank System Settings
const RankRequirements = {
  BRONZE: {
    level: 1,
    minPurchase: 0,
    minCommission: 0,
    minReferrals: 0,
  },
  SILVER: {
    level: 2,
    minPurchase: 1000000,
    minCommission: 500000,
    minReferrals: 5,
  },
  GOLD: {
    level: 3,
    minPurchase: 5000000,
    minCommission: 2000000,
    minReferrals: 10,
  },
  PLATINUM: {
    level: 4,
    minPurchase: 10000000,
    minCommission: 5000000,
    minReferrals: 20,
  },
  DIAMOND: {
    level: 5,
    minPurchase: 50000000,
    minCommission: 20000000,
    minReferrals: 50,
  },
};

// Default Ranks Configuration
const DefaultRanks = [
  {
    id: 1,
    name: "Thành viên Đồng",
    description: "Cấp bậc cơ bản cho thành viên mới",
    level: 1,
    requirements: RankRequirements.BRONZE,
    benefits: {
      commissionRate: 5,
      discountRate: 0,
      specialProducts: [],
    },
    color: "#CD7F32",
    icon: "bronze-medal",
  },
  {
    id: 2,
    name: "Thành viên Bạc",
    description: "Cấp bậc cho thành viên tích cực",
    level: 2,
    requirements: RankRequirements.SILVER,
    benefits: {
      commissionRate: 7,
      discountRate: 5,
      specialProducts: [],
    },
    color: "#C0C0C0",
    icon: "silver-medal",
  },
  {
    id: 3,
    name: "Thành viên Vàng",
    description: "Cấp bậc cho thành viên xuất sắc",
    level: 3,
    requirements: RankRequirements.GOLD,
    benefits: {
      commissionRate: 10,
      discountRate: 10,
      specialProducts: [],
    },
    color: "#FFD700",
    icon: "gold-medal",
  },
  {
    id: 4,
    name: "Thành viên Bạch Kim",
    description: "Cấp bậc cao cấp",
    level: 4,
    requirements: RankRequirements.PLATINUM,
    benefits: {
      commissionRate: 15,
      discountRate: 15,
      specialProducts: [],
    },
    color: "#E5E4E2",
    icon: "platinum-medal",
  },
  {
    id: 5,
    name: "Thành viên Kim Cương",
    description: "Cấp bậc cao nhất",
    level: 5,
    requirements: RankRequirements.DIAMOND,
    benefits: {
      commissionRate: 20,
      discountRate: 20,
      specialProducts: [],
    },
    color: "#B9F2FF",
    icon: "diamond",
  },
];

module.exports = {
  Level,
  ColabStatus,
  TransactionType,
  DirectCommission,
  NewUserRegisterCommissionValue,
  NewUserRegisterCommissionValueForPresident,
  PREFIX_CODE,
  REFER_CODE_ROOT,
  MustVerifyPhone,
  RankRequirements,
  DefaultRanks,
};
