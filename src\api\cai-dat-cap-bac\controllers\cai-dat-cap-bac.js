"use strict";

/**
 * cai-dat-cap-bac controller
 */

const { createCoreController } = require("@strapi/strapi").factories;

module.exports = createCoreController(
  "api::cai-dat-cap-bac.cai-dat-cap-bac",
  ({ strapi }) => ({
    /**
     * Get rank settings
     */
    async getRankSettings(ctx) {
      try {
        const rankService = strapi.service("api::cai-dat-cap-bac.rank-service");
        const settings = await rankService.getRankSettings();

        ctx.body = {
          success: true,
          data: settings,
        };
      } catch (error) {
        console.error("Error getting rank settings:", error);
        ctx.throw(500, "Không thể tải cài đặt cấp bậc");
      }
    },

    /**
     * Update rank settings
     */
    async updateRankSettings(ctx) {
      try {
        const rankService = strapi.service("api::cai-dat-cap-bac.rank-service");
        const settings = await rankService.updateRankSettings(ctx.request.body);

        ctx.body = {
          success: true,
          data: settings,
        };
      } catch (error) {
        console.error("Error updating rank settings:", error);
        ctx.throw(500, "Không thể cập nhật cài đặt cấp bậc");
      }
    },

    /**
     * Get user rank
     */
    async getUserRank(ctx) {
      try {
        const { userId } = ctx.params;
        const rankService = strapi.service("api::cai-dat-cap-bac.rank-service");
        const rank = await rankService.getUserRank(parseInt(userId));

        ctx.body = {
          success: true,
          data: rank,
        };
      } catch (error) {
        console.error("Error getting user rank:", error);
        ctx.throw(500, "Không thể tải cấp bậc người dùng");
      }
    },

    /**
     * Calculate eligible rank for user
     */
    async calculateEligibleRank(ctx) {
      try {
        const { userId } = ctx.params;
        const rankService = strapi.service("api::cai-dat-cap-bac.rank-service");
        const eligibleRank = await rankService.calculateEligibleRank(
          parseInt(userId)
        );

        ctx.body = {
          success: true,
          data: eligibleRank,
        };
      } catch (error) {
        console.error("Error calculating eligible rank:", error);
        ctx.throw(500, "Không thể tính toán cấp bậc phù hợp");
      }
    },

    /**
     * Upgrade user rank
     */
    async upgradeUserRank(ctx) {
      try {
        const { userId } = ctx.params;
        const rankService = strapi.service("api::cai-dat-cap-bac.rank-service");
        const upgradedRank = await rankService.autoUpgradeUserRank(
          parseInt(userId)
        );

        if (upgradedRank) {
          ctx.body = {
            success: true,
            message: "Cấp bậc đã được nâng cấp thành công",
            data: upgradedRank,
          };
        } else {
          ctx.body = {
            success: false,
            message: "Người dùng chưa đủ điều kiện nâng cấp",
            data: null,
          };
        }
      } catch (error) {
        console.error("Error upgrading user rank:", error);
        ctx.throw(500, "Không thể nâng cấp cấp bậc");
      }
    },

    /**
     * Get all available ranks
     */
    async getAllRanks(ctx) {
      try {
        const rankService = strapi.service("api::cai-dat-cap-bac.rank-service");
        const ranks = await rankService.getAllRanks();

        ctx.body = {
          success: true,
          data: ranks,
        };
      } catch (error) {
        console.error("Error getting all ranks:", error);
        ctx.throw(500, "Không thể tải danh sách cấp bậc");
      }
    },
  })
);
