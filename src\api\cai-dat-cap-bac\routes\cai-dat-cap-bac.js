"use strict";

/**
 * cai-dat-cap-bac router
 */

const { createCoreRouter } = require("@strapi/strapi").factories;

// Create default routes
const defaultRouter = createCoreRouter("api::cai-dat-cap-bac.cai-dat-cap-bac");

// Custom routes for rank management
const customRoutes = {
  routes: [
    // Rank settings routes
    {
      method: "GET",
      path: "/cai-dat-cap-bac/settings",
      handler: "cai-dat-cap-bac.getRankSettings",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "PUT",
      path: "/cai-dat-cap-bac/settings",
      handler: "cai-dat-cap-bac.updateRankSettings",
      config: {
        policies: [],
        middlewares: [],
      },
    },

    // User rank routes
    {
      method: "GET",
      path: "/cai-dat-cap-bac/user/:userId/rank",
      handler: "cai-dat-cap-bac.getUserRank",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "GET",
      path: "/cai-dat-cap-bac/user/:userId/eligible-rank",
      handler: "cai-dat-cap-bac.calculateEligibleRank",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "POST",
      path: "/cai-dat-cap-bac/user/:userId/upgrade",
      handler: "cai-dat-cap-bac.upgradeUserRank",
      config: {
        policies: [],
        middlewares: [],
      },
    },

    // General rank routes
    {
      method: "GET",
      path: "/cai-dat-cap-bac/ranks",
      handler: "cai-dat-cap-bac.getAllRanks",
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};

// Merge default routes with custom routes
module.exports = {
  routes: [...defaultRouter.routes, ...customRoutes.routes],
};
