"use strict";

/**
 * cai-dat-cap-bac service
 */

const { createCoreService } = require("@strapi/strapi").factories;

module.exports = createCoreService(
  "api::cai-dat-cap-bac.cai-dat-cap-bac",
  ({ strapi }) => ({
    // Add any custom methods here if needed

    /**
     * Initialize rank system with default settings
     */
    async initializeRankSystem() {
      try {
        const rankService = strapi.service("api::cai-dat-cap-bac.rank-service");
        const settings = await rankService.getRankSettings();

        console.log("Rank system initialized:", settings);
        return settings;
      } catch (error) {
        console.error("Error initializing rank system:", error);
        throw error;
      }
    },
  })
);
