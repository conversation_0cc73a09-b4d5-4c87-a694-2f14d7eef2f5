const request = require('supertest');
const { setupStrapi, cleanupStrapi } = require('./helpers/strapi');

describe('Zalo Mini App Authentication', () => {
  let strapi;
  let agent;

  beforeAll(async () => {
    strapi = await setupStrapi();
    agent = request.agent(strapi.server.httpServer);
  });

  afterAll(async () => {
    await cleanupStrapi(strapi);
  });

  describe('POST /api/auth/zalo', () => {
    it('should reject request without access token', async () => {
      const response = await agent
        .post('/api/auth/zalo')
        .send({
          phone: '0987654321'
        })
        .expect(400);

      expect(response.body.error.message).toContain('Access token Zalo là bắt buộc');
    });

    it('should reject invalid access token', async () => {
      const response = await agent
        .post('/api/auth/zalo')
        .send({
          accessToken: 'invalid_token',
          phone: '0987654321'
        })
        .expect(400);

      expect(response.body.error.message).toContain('Access token không hợp lệ');
    });

    it('should create new user with valid Zalo token', async () => {
      // Mock Zalo service for testing
      const mockZaloService = {
        validateAccessToken: jest.fn().mockResolvedValue({
          isValid: true,
          zaloId: 'test_zalo_id_123',
          zaloName: 'Test User',
          zaloAvatar: 'https://example.com/avatar.jpg'
        }),
        logAuthEvent: jest.fn(),
        sendOAMessage: jest.fn()
      };

      // Replace the service temporarily
      const originalService = require('../src/services/zalo-service');
      Object.assign(originalService, mockZaloService);

      const response = await agent
        .post('/api/auth/zalo')
        .send({
          accessToken: 'valid_test_token',
          phone: '0987654321'
        })
        .expect(200);

      expect(response.body.jwt).toBeDefined();
      expect(response.body.user).toBeDefined();
      expect(response.body.user.zaloId).toBe('test_zalo_id_123');
      expect(response.body.isNewUser).toBe(true);
    });

    it('should login existing user with Zalo ID', async () => {
      // First create a user
      const userData = {
        username: 'zalo_test_user',
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
        phone: '0987654322',
        zaloId: 'existing_zalo_id',
        confirmed: true
      };

      const user = await strapi.plugins['users-permissions'].services.user.add(userData);

      // Mock Zalo service
      const mockZaloService = {
        validateAccessToken: jest.fn().mockResolvedValue({
          isValid: true,
          zaloId: 'existing_zalo_id',
          zaloName: 'Test User Updated',
          zaloAvatar: 'https://example.com/new_avatar.jpg'
        }),
        logAuthEvent: jest.fn()
      };

      const originalService = require('../src/services/zalo-service');
      Object.assign(originalService, mockZaloService);

      const response = await agent
        .post('/api/auth/zalo')
        .send({
          accessToken: 'valid_existing_token'
        })
        .expect(200);

      expect(response.body.jwt).toBeDefined();
      expect(response.body.user.id).toBe(user.id);
      expect(response.body.isNewUser).toBe(false);
    });

    it('should handle referral code correctly', async () => {
      // Create referrer user
      const referrerData = {
        username: 'referrer_user',
        email: '<EMAIL>',
        password: 'password123',
        name: 'Referrer User',
        phone: '0987654323',
        confirmed: true
      };

      await strapi.plugins['users-permissions'].services.user.add(referrerData);

      // Mock Zalo service
      const mockZaloService = {
        validateAccessToken: jest.fn().mockResolvedValue({
          isValid: true,
          zaloId: 'new_referred_user',
          zaloName: 'Referred User',
          zaloAvatar: 'https://example.com/referred_avatar.jpg'
        }),
        logAuthEvent: jest.fn(),
        sendOAMessage: jest.fn()
      };

      const originalService = require('../src/services/zalo-service');
      Object.assign(originalService, mockZaloService);

      const response = await agent
        .post('/api/auth/zalo')
        .send({
          accessToken: 'valid_referred_token',
          phone: '0987654324',
          referCode: '0987654323' // Referrer's phone
        })
        .expect(200);

      expect(response.body.user.referUser).toBeDefined();
    });

    it('should reject invalid referral code', async () => {
      // Mock Zalo service
      const mockZaloService = {
        validateAccessToken: jest.fn().mockResolvedValue({
          isValid: true,
          zaloId: 'test_invalid_refer',
          zaloName: 'Test User',
          zaloAvatar: 'https://example.com/avatar.jpg'
        })
      };

      const originalService = require('../src/services/zalo-service');
      Object.assign(originalService, mockZaloService);

      const response = await agent
        .post('/api/auth/zalo')
        .send({
          accessToken: 'valid_test_token',
          phone: '0987654325',
          referCode: '0999999999' // Non-existent phone
        })
        .expect(400);

      expect(response.body.error.message).toContain('người giới thiệu không hợp lệ');
    });
  });

  describe('POST /api/auth/link-zalo', () => {
    let authToken;
    let userId;

    beforeEach(async () => {
      // Create and login a user
      const userData = {
        username: 'link_test_user',
        email: '<EMAIL>',
        password: 'password123',
        name: 'Link Test User',
        phone: '0987654326',
        confirmed: true
      };

      const user = await strapi.plugins['users-permissions'].services.user.add(userData);
      userId = user.id;

      // Get JWT token
      const jwt = strapi.plugins['users-permissions'].services.jwt.issue({ id: user.id });
      authToken = jwt;
    });

    it('should require authentication', async () => {
      const response = await agent
        .post('/api/auth/link-zalo')
        .send({
          accessToken: 'valid_token'
        })
        .expect(401);
    });

    it('should link Zalo account to existing user', async () => {
      // Mock Zalo service
      const mockZaloService = {
        validateAccessToken: jest.fn().mockResolvedValue({
          isValid: true,
          zaloId: 'link_zalo_id_123',
          zaloName: 'Linked User',
          zaloAvatar: 'https://example.com/linked_avatar.jpg'
        }),
        logAuthEvent: jest.fn()
      };

      const originalService = require('../src/services/zalo-service');
      Object.assign(originalService, mockZaloService);

      const response = await agent
        .post('/api/auth/link-zalo')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          accessToken: 'valid_link_token'
        })
        .expect(200);

      expect(response.body.user.zaloId).toBe('link_zalo_id_123');
      expect(response.body.message).toContain('thành công');
    });

    it('should reject linking already linked Zalo account', async () => {
      // Create another user with the same Zalo ID
      const existingUserData = {
        username: 'existing_zalo_user',
        email: '<EMAIL>',
        password: 'password123',
        name: 'Existing Zalo User',
        phone: '**********',
        zaloId: 'already_linked_zalo_id',
        confirmed: true
      };

      await strapi.plugins['users-permissions'].services.user.add(existingUserData);

      // Mock Zalo service
      const mockZaloService = {
        validateAccessToken: jest.fn().mockResolvedValue({
          isValid: true,
          zaloId: 'already_linked_zalo_id',
          zaloName: 'Already Linked User',
          zaloAvatar: 'https://example.com/avatar.jpg'
        })
      };

      const originalService = require('../src/services/zalo-service');
      Object.assign(originalService, mockZaloService);

      const response = await agent
        .post('/api/auth/link-zalo')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          accessToken: 'already_linked_token'
        })
        .expect(400);

      expect(response.body.error.message).toContain('đã được liên kết');
    });
  });
});
